@echo off
echo ========================================
echo    SETUP DU AN WINFORMS - QUAN LY DE TAI
echo ========================================

echo.
echo [1/5] Kiem tra .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK khong duoc cai dat!
    echo Vui long tai va cai dat .NET 8.0 SDK
    pause
    exit /b 1
)

echo.
echo [2/5] Clean solution...
dotnet clean WinFormsApp1.sln
if %errorlevel% neq 0 (
    echo WARNING: Khong the clean solution!
)

echo.
echo [3/5] Restore dependencies...
dotnet restore WinFormsApp1.sln
if %errorlevel% neq 0 (
    echo ERROR: Khong the restore dependencies!
    pause
    exit /b 1
)

echo.
echo [4/5] Tao va cap nhat database...

:: Xoa database cu neu ton tai
sqlcmd -S .\sqlexpress -E -Q "IF EXISTS (SELECT name FROM sys.databases WHERE name = 'Tung_DB') DROP DATABASE Tung_DB"

:: Thu khoi phuc tu file backup
echo Dang thu khoi phuc database tu file backup...
set BACKUP_FILE=%~dp0Tung_DB.bak

:: Lay thong tin logical file names tu file backup
for /f "tokens=1,2" %%a in ('sqlcmd -S .\sqlexpress -E -Q "RESTORE FILELISTONLY FROM DISK='%BACKUP_FILE%'" ^| findstr "Tung_DB"') do (
    set LOGICAL_NAME=%%a
    set PHYSICAL_NAME=%%b
)

:: Lay duong dan data cua SQL Server
for /f "tokens=1,2,3" %%a in ('sqlcmd -S .\sqlexpress -E -Q "SELECT SERVERPROPERTY('InstanceDefaultDataPath') AS DataPath"') do (
    set DATA_PATH=%%b
)

:: Khoi phuc database
sqlcmd -S .\sqlexpress -E -Q "RESTORE DATABASE Tung_DB FROM DISK='%BACKUP_FILE%' WITH MOVE 'Tung_DB' TO '%DATA_PATH%Tung_DB.mdf', MOVE 'Tung_DB_log' TO '%DATA_PATH%Tung_DB_log.ldf', REPLACE"

:: Kiem tra ket qua khoi phuc
if %errorlevel% neq 0 (
    echo WARNING: Khong the khoi phuc database tu file backup!
    echo Dang chuyen sang phuong an tao database moi va chay migration...
    
    :: Tao database moi
    sqlcmd -S .\sqlexpress -E -Q "CREATE DATABASE Tung_DB"
    if %errorlevel% neq 0 (
        echo ERROR: Khong the tao database!
        pause
        exit /b 1
    )
    
    :: Chay migration
    echo Dang chay migration...
    cd Models
    dotnet ef database update --startup-project ../WinFormsApp1
    if %errorlevel% neq 0 (
        echo ERROR: Khong the chay migration!
        cd ..
        pause
        exit /b 1
    )
    cd ..
) else (
    echo Database da duoc khoi phuc thanh cong tu file backup!
)

echo.
echo [5/5] Build solution...
dotnet build WinFormsApp1.sln --configuration Debug
if %errorlevel% neq 0 (
    echo ERROR: Khong the build solution!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    SETUP HOAN THANH!
echo ========================================
echo.
echo De chay ung dung, su dung file run.bat
echo.
pause